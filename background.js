// Background script for Perplexity Pro Model Selector

let selectedModel = null;

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'updateSelectedModel') {
        selectedModel = request.model;
        console.log('Background: Updated selected model to:', selectedModel, 'from text:', request.modelText);
        
        // Store in chrome.storage for persistence
        chrome.storage.local.set({
            selectedModel: selectedModel,
            selectedModelText: request.modelText
        });
        
        sendResponse({success: true});
    }
    
    if (request.action === 'getSelectedModel') {
        sendResponse({model: selectedModel});
    }
});

// Initialize on startup
chrome.runtime.onStartup.addListener(() => {
    // Restore selected model from storage
    chrome.storage.local.get(['selectedModel'], (result) => {
        if (result.selectedModel) {
            selectedModel = result.selectedModel;
            console.log('Background: Restored selected model:', selectedModel);
        }
    });
});

// Initialize on install
chrome.runtime.onInstalled.addListener(() => {
    console.log('Perplexity Pro Model Selector extension installed');
    
    // Restore selected model from storage
    chrome.storage.local.get(['selectedModel'], (result) => {
        if (result.selectedModel) {
            selectedModel = result.selectedModel;
            console.log('Background: Restored selected model:', selectedModel);
        }
    });
});
