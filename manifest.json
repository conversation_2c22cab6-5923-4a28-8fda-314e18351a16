{"manifest_version": 3, "name": "Perplexity Pro Model Selector", "version": "1.0", "description": "Intercepts Perplexity.ai model selection and modifies API requests", "permissions": ["activeTab", "declarativeNetRequest", "declarativeNetRequestWithHostAccess", "storage"], "host_permissions": ["*://*.perplexity.ai/*"], "content_scripts": [{"matches": ["*://*.perplexity.ai/*"], "js": ["content.js"], "run_at": "document_start"}], "background": {"service_worker": "background.js"}, "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}}