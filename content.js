// Perplexity Pro Model Selector Content Script

// Model mapping table
const models = {
    'Best': 'pplx_pro',
    'Sonar': 'experimental',
    'GPT-5': 'gpt5',
    'GPT-5 Thinking': 'gpt5_thinking',
    '<PERSON> Sonnet 4.0': 'claude2',
    '<PERSON> Sonnet 4.0 Thinking': 'claude<PERSON><PERSON><PERSON>hinking',
    'Claude Opus 4.1 Thinking': 'claude<PERSON><PERSON>netthinking', // Added this based on the HTML
    'Gemini 2.5 Pro': 'gemini2flash',
    'Grok 4': 'grok4',
    'o3': 'o3',
    'o3-pro': 'o3' // Added this based on the HTML
};

let selectedModel = null;

// Function to find the best menu with items
function findBestMenuWithItems() {
    const menus = document.querySelectorAll('[role="menu"]');

    for (const menu of menus) {
        const menuItems = menu.querySelectorAll('[role="menuitem"]');
        // Check if there's at least one menu item.
        if (menuItems.length > 0) {
            // Check if there's a descendant span with the text "Best".
            const bestSpan = menu.querySelector('span');
            if (bestSpan && bestSpan.textContent.trim() === 'Best') {
                return menu;
            }
        }
    }

    return null;
}

// Function to intercept clicks on menu items
function interceptMenuClicks() {
    const menu = findBestMenuWithItems();
    if (!menu) {
        return;
    }

    const menuItems = menu.querySelectorAll('[role="menuitem"]');
    
    menuItems.forEach((menuItem, index) => {
        // Remove any existing listeners to avoid duplicates
        menuItem.removeEventListener('click', handleMenuItemClick);
        
        // Add click event listener
        menuItem.addEventListener('click', handleMenuItemClick, true);
    });
}

// Handle menu item clicks
function handleMenuItemClick(event) {
    // Prevent the default action
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
    
    // Get the text content of the clicked item
    const spanElement = event.currentTarget.querySelector('span');
    if (spanElement) {
        const modelText = spanElement.textContent.trim();
        console.log('Model button clicked:', modelText);
        
        // Store the selected model
        selectedModel = models[modelText] || modelText;
        console.log('Selected model mapped to:', selectedModel);
        
        // Send message to background script to update the selected model
        chrome.runtime.sendMessage({
            action: 'updateSelectedModel',
            model: selectedModel,
            modelText: modelText
        });
    }
}

// Override XMLHttpRequest to intercept network requests
function interceptNetworkRequests() {
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._method = method;
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(data) {
        if (this._method === 'POST' && 
            this._url && 
            this._url.includes('https://www.perplexity.ai/rest/sse/perplexity_ask')) {
            
            console.log('Intercepted request to perplexity_ask');
            
            if (data && selectedModel) {
                try {
                    const payload = JSON.parse(data);
                    if (payload.params && payload.params.model_preference !== undefined) {
                        console.log('Original model_preference:', payload.params.model_preference);
                        payload.params.model_preference = selectedModel;
                        console.log('Modified model_preference to:', selectedModel);
                        
                        // Send the modified payload
                        return originalXHRSend.call(this, JSON.stringify(payload));
                    }
                } catch (error) {
                    console.error('Error parsing/modifying payload:', error);
                }
            }
        }
        
        return originalXHRSend.call(this, data);
    };
}

// Override fetch API as well
function interceptFetchRequests() {
    const originalFetch = window.fetch;
    
    window.fetch = function(url, options = {}) {
        if (typeof url === 'string' && 
            url.includes('https://www.perplexity.ai/rest/sse/perplexity_ask') &&
            options.method === 'POST' &&
            options.body &&
            selectedModel) {
            
            console.log('Intercepted fetch request to perplexity_ask');
            
            try {
                const payload = JSON.parse(options.body);
                if (payload.params && payload.params.model_preference !== undefined) {
                    console.log('Original model_preference:', payload.params.model_preference);
                    payload.params.model_preference = selectedModel;
                    console.log('Modified model_preference to:', selectedModel);
                    
                    // Create new options with modified body
                    const newOptions = {
                        ...options,
                        body: JSON.stringify(payload)
                    };
                    
                    return originalFetch.call(this, url, newOptions);
                }
            } catch (error) {
                console.error('Error parsing/modifying fetch payload:', error);
            }
        }
        
        return originalFetch.call(this, url, options);
    };
}

// Initialize the extension
function initialize() {
    console.log('Perplexity Pro Model Selector initialized');
    
    // Set up network request interception
    interceptNetworkRequests();
    interceptFetchRequests();
    
    // Set up menu click interception
    interceptMenuClicks();
    
    // Use MutationObserver to watch for DOM changes and re-attach listeners
    const observer = new MutationObserver((mutations) => {
        let shouldRecheck = false;
        
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if any added nodes contain menu elements
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.querySelector && 
                            (node.querySelector('[role="menu"]') || 
                             node.getAttribute('role') === 'menu')) {
                            shouldRecheck = true;
                        }
                    }
                });
            }
        });
        
        if (shouldRecheck) {
            setTimeout(interceptMenuClicks, 100); // Small delay to ensure DOM is ready
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// Start when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}

// Also try to initialize after a short delay to catch dynamically loaded content
setTimeout(initialize, 1000);
