# Perplexity Pro Model Selector Extension

A Chrome browser extension that intercepts model selection on Perplexity.ai and modifies API requests to use the selected model.

## Features

- Detects and intercepts clicks on Perplexity.ai model selection menu
- Prevents default model selection behavior
- Logs selected model to console
- Intercepts network requests to `https://www.perplexity.ai/rest/sse/perplexity_ask`
- Modifies the `model_preference` parameter in the request payload
- Supports all current Perplexity models including GPT-5, Claude Sonnet 4.0, Gemini 2.5 Pro, etc.

## Supported Models

The extension maps the following model names to their API values:

- **Best** → `pplx_pro`
- **Sonar** → `experimental`
- **GPT-5** → `gpt5`
- **GPT-5 Thinking** → `gpt5_thinking`
- **Claude Sonnet 4.0** → `claude2`
- **Claude Sonnet 4.0 Thinking** → `claude<PERSON><PERSON><PERSON>hinking`
- **Claude Opus 4.1 Thinking** → `claude<PERSON><PERSON>netthinking`
- **Gemini 2.5 Pro** → `gemini2flash`
- **Grok 4** → `grok4`
- **o3** → `o3`
- **o3-pro** → `o3`

## Installation

1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the folder containing the extension files
5. The extension should now be loaded and active

## Usage

1. Navigate to any Perplexity.ai page
2. Open the browser's Developer Console (F12 → Console tab)
3. Click on any model in the model selection menu
4. The extension will:
   - Prevent the default click behavior
   - Log the selected model name to the console
   - Store the selection for future API requests
5. When you make a query, the extension will automatically modify the API request to use your selected model

## How It Works

1. **Menu Detection**: The extension uses the `findBestMenuWithItems()` function to locate the model selection menu
2. **Click Interception**: Event listeners are attached to menu items to prevent default behavior and capture selections
3. **Network Interception**: Both XMLHttpRequest and fetch APIs are overridden to intercept requests
4. **Payload Modification**: When a request to the Perplexity API is detected, the `model_preference` parameter is modified

## Files

- `manifest.json` - Extension configuration
- `content.js` - Main content script that runs on Perplexity.ai pages
- `background.js` - Background service worker for message handling
- `rules.json` - Declarative net request rules
- `README.md` - This documentation

## Development

To modify the model mappings, edit the `models` object in `content.js`:

```javascript
const models = {
    'Model Display Name': 'api_model_value',
    // Add new mappings here
};
```

## Troubleshooting

- Make sure the extension is enabled in Chrome extensions
- Check the browser console for any error messages
- Ensure you're on a Perplexity.ai domain
- Try refreshing the page if the extension doesn't seem to be working

## Permissions

The extension requires the following permissions:
- `activeTab` - To interact with the current tab
- `declarativeNetRequest` - To modify network requests
- `declarativeNetRequestWithHostAccess` - To access Perplexity.ai requests
- Host permission for `*://*.perplexity.ai/*` - To run on Perplexity domains
